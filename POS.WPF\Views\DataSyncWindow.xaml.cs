﻿using System.Text.Json;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using POS.WPF.LocalData;
using POS.WPF.Tests;
using POS.WPF.Views;
using TimeManagement;

namespace POS.WPF.Views
{
    public partial class DataSyncWindow : Window
    {
        public DataSyncWindow()
        {
            InitializeComponent();

            ////temp
            ///

            StartSync();

        }

        public class SaleComparisonDto
        {
            public int SaleId { get; set; }
            public DateTime Date { get; set; }
            public decimal TotalValue { get; set; }
            public decimal Cashback { get; set; }
            public bool IsOnHold { get; set; }

            public decimal? CashTendered { get; set; }
            public string? Barcode { get; set; }
            public int ShiftId { get; set; }
        }




        private async void StartSync()
        {
            try
            {
                // Run the sync process
                await Task.Run(() => Services.RemoteToLocalSyncService.SyncAll());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during synchronization: {ex.Message}", "Sync Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // After sync is complete (success or failure), show login window and close this window
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var loginWindow = new LoginWindow();
                    loginWindow.Show();
                    this.Close();
                });
            }
        }

        // SyncAll method removed as it's now in DataSyncService
    }
}
